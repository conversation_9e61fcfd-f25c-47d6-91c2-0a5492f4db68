import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_database/firebase_database.dart';

class IrrigationSystem {
  final String id;
  final String name;
  final bool isConnected;
  final String serial;
  final String activationCode;

  IrrigationSystem({
    required this.id,
    required this.name,
    required this.isConnected,
    required this.serial,
    required this.activationCode,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'isConnected': isConnected,
      'serial': serial,
      'activationCode': activationCode,
    };
  }

  factory IrrigationSystem.fromMap(Map<String, dynamic> map) {
    return IrrigationSystem(
      id: map['id']?.toString() ?? UniqueKey().toString(),
      name: map['name']?.toString() ?? 'نظام غير معروف',
      isConnected: map['isConnected'] is bool
          ? map['isConnected'] as bool
          : map['isConnected']?.toString().toLowerCase() == 'true',
      serial: map['serial']?.toString() ?? '',
      activationCode: map['activationCode']?.toString() ?? '',
    );
  }
}

class IrrigationRecord {
  final String systemName;
  final DateTime startTime;
  final DateTime? endTime;
  final String mode;
  final double waterConsumption;
  final double? initialWaterLevel;
  final double? finalWaterLevel;

  IrrigationRecord({
    required this.systemName,
    required this.startTime,
    this.endTime,
    required this.mode,
    required this.waterConsumption,
    this.initialWaterLevel,
    this.finalWaterLevel,
  });

  String get formattedDate {
    return '${startTime.day}/${startTime.month}/${startTime.year} ${startTime.hour}:${startTime.minute.toString().padLeft(2, '0')}';
  }

  String get formattedMode {
    return mode == 'manual' ? 'يدوي' : 'ذكي';
  }

  Duration? get duration {
    return endTime?.difference(startTime);
  }

  Map<String, dynamic> toMap() {
    return {
      'systemName': systemName,
      'startTime': startTime.millisecondsSinceEpoch,
      'endTime': endTime?.millisecondsSinceEpoch,
      'mode': mode,
      'waterConsumption': waterConsumption,
      'initialWaterLevel': initialWaterLevel,
      'finalWaterLevel': finalWaterLevel,
    };
  }

  factory IrrigationRecord.fromMap(Map<String, dynamic> map) {
    return IrrigationRecord(
      systemName: map['systemName'] ?? '',
      startTime: DateTime.fromMillisecondsSinceEpoch(map['startTime']),
      endTime: map['endTime'] != null ? DateTime.fromMillisecondsSinceEpoch(map['endTime']) : null,
      mode: map['mode'] ?? 'manual',
      waterConsumption: (map['waterConsumption'] ?? 0.0).toDouble(),
      initialWaterLevel: map['initialWaterLevel']?.toDouble(),
      finalWaterLevel: map['finalWaterLevel']?.toDouble(),
    );
  }
}

double _toDouble(dynamic value, {double defaultValue = 0.0}) {
  if (value == null) return defaultValue;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  if (value is String) {
    return double.tryParse(value) ?? defaultValue;
  }
  return defaultValue;
}

class SmartIrrigationSettings {
  final double minSoilMoisture;
  final double maxTemperature;
  final double minWaterLevel;
  final bool ignoreRain;
  final double optimalHumidity;
  final DateTime updatedAt;

  SmartIrrigationSettings({
    required this.minSoilMoisture,
    required this.maxTemperature,
    required this.minWaterLevel,
    required this.ignoreRain,
    required this.optimalHumidity,
    required this.updatedAt,
  });

  factory SmartIrrigationSettings.fromMap(Map<String, dynamic> map) {
    return SmartIrrigationSettings(
      minSoilMoisture: _toDouble(map['minSoilMoisture'], defaultValue: 60.0),
      maxTemperature: _toDouble(map['maxTemperature'], defaultValue: 35.0),
      minWaterLevel: _toDouble(map['minWaterLevel'], defaultValue: 20.0),
      ignoreRain: map['ignoreRain'] ?? false,
      optimalHumidity: _toDouble(map['optimalHumidity']),
      updatedAt: map['updatedAt'] is Timestamp 
          ? (map['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'minSoilMoisture': minSoilMoisture,
      'maxTemperature': maxTemperature,
      'minWaterLevel': minWaterLevel,
      'ignoreRain': ignoreRain,
      'optimalHumidity': optimalHumidity,
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }
}

class FirebaseConnectionManager {
  final FirebaseFirestore _firestore;
  final FirebaseDatabase _realtimeDb;
  bool _isConnected = true;
  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();
  
  FirebaseConnectionManager(this._firestore, this._realtimeDb) {
    _initConnectionMonitoring();
  }
  
  void _initConnectionMonitoring() {
    Connectivity().onConnectivityChanged.listen((result) async {
      final newStatus = result != ConnectivityResult.none;
      if (newStatus != _isConnected) {
        _isConnected = newStatus;
        _connectionController.add(_isConnected);
        
        if (_isConnected) {
          try {
            await _firestore.collection('irrigation_systems').limit(1).get().timeout(const Duration(seconds: 5));
          } catch (e) {
            _isConnected = false;
            _connectionController.add(false);
          }
        }
      }
    });
  }
  
  Stream<bool> get connectionStream => _connectionController.stream;
  
  Future<T> executeWithRetry<T>(Future<T> Function() action, 
      {int maxRetries = 3, Duration? retryDelay}) async {
    int attempts = 0;
    while (attempts < maxRetries) {
      try {
        final result = await action().timeout(const Duration(seconds: 15));
        _isConnected = true;
        return result;
      } catch (e) {
        attempts++;
        _isConnected = false;
        _connectionController.add(false);
        if (attempts >= maxRetries) rethrow;
        await Future.delayed(retryDelay ?? Duration(seconds: 2 * attempts));
      }
    }
    throw Exception('فشل العملية بعد $maxRetries محاولات');
  }
  
  bool get isConnected => _isConnected;
  
  void dispose() {
    _connectionController.close();
  }
}

class FirebaseIrrigationService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseDatabase _realtimeDb = FirebaseDatabase.instance;
  late final FirebaseConnectionManager connectionManager;

  FirebaseIrrigationService() {
    connectionManager = FirebaseConnectionManager(_firestore, _realtimeDb);
  }

  // جلب أنظمة الري
  Future<List<IrrigationSystem>> getIrrigationSystems() async {
    try {
      final snapshot = await connectionManager.executeWithRetry(
        () => _firestore.collection('irrigation_systems').get(),
      );
      
      return snapshot.docs.map((doc) {
        final data = doc.data();
        return IrrigationSystem(
          id: doc.id,
          name: data['name'] ?? 'نظام غير معروف',
          isConnected: data['isConnected'] ?? false,
          serial: data['serial'] ?? '',
          activationCode: data['activationCode'] ?? '',
        );
      }).toList();
    } catch (e) {
      debugPrint('Error getting irrigation systems: $e');
      return [];
    }
  }

  // الاتصال بنظام جديد
  Future<Map<String, dynamic>> connectSystem(String activationCode) async {
    try {
      // البحث عن النظام بكود التفعيل
      final systemQuery = await connectionManager.executeWithRetry(
        () => _firestore
            .collection('irrigation_systems')
            .where('activationCode', isEqualTo: activationCode)
            .get(),
      );

      if (systemQuery.docs.isEmpty) {
        return {
          'status': 'error',
          'message': 'كود التفعيل غير صحيح أو النظام غير موجود',
        };
      }

      final systemDoc = systemQuery.docs.first;
      
      // تحديث حالة الاتصال
      await connectionManager.executeWithRetry(
        () => systemDoc.reference.update({'isConnected': true}),
      );

      return {
        'status': 'success',
        'message': 'تم الاتصال بنجاح',
        'system_id': systemDoc.id,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'فشل الاتصال: ${e.toString()}',
      };
    }
  }

  // قطع الاتصال بالنظام
  Future<Map<String, dynamic>> disconnectSystem(String activationCode) async {
    try {
      final systemQuery = await connectionManager.executeWithRetry(
        () => _firestore
            .collection('irrigation_systems')
            .where('activationCode', isEqualTo: activationCode)
            .get(),
      );

      if (systemQuery.docs.isNotEmpty) {
        await connectionManager.executeWithRetry(
          () => systemQuery.docs.first.reference.update({'isConnected': false}),
        );
      }

      return {
        'status': 'success',
        'message': 'تم قطع الاتصال بنجاح',
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'فشل قطع الاتصال: ${e.toString()}',
      };
    }
  }

  // جلب بيانات الحساسات من Realtime Database
  Future<Map<String, dynamic>> getSensorData(String activationCode) async {
    try {
      final ref = _realtimeDb.ref('sensors/$activationCode');
      final snapshot = await connectionManager.executeWithRetry(
        () => ref.get(),
      );

      if (!snapshot.exists) {
        return _getDefaultSensorValues();
      }

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final processedData = <String, dynamic>{};

      data.forEach((key, value) {
        processedData[key] = {
          'value': _toDouble(value['value']),
          'unit': value['unit'] ?? _getDefaultUnit(key),
          'name_ar': _getSensorArabicName(key),
          'icon': _getSensorIconData(key),
          'color': _getSensorColorData(key, _toDouble(value['value'])),
          'timestamp': value['timestamp'] ?? DateTime.now().millisecondsSinceEpoch,
        };
      });

      return processedData;
    } catch (e) {
      debugPrint('Error getting sensor data: $e');
      return _getDefaultSensorValues();
    }
  }

  // جلب حالة المضخة
  Future<Map<String, dynamic>> getPumpStatus(String systemId) async {
    try {
      final doc = await connectionManager.executeWithRetry(
        () => _firestore.collection('pump_status').doc(systemId).get(),
      );

      if (!doc.exists) {
        return {
          'isPumpOn': false,
          'currentMode': 'manual',
        };
      }

      final data = doc.data()!;
      return {
        'isPumpOn': data['isPumpOn'] ?? false,
        'currentMode': data['currentModeId'] == 1 ? 'manual' : 'smart',
      };
    } catch (e) {
      debugPrint('Error getting pump status: $e');
      return {
        'isPumpOn': false,
        'currentMode': 'manual',
      };
    }
  }

  // تحديث حالة المضخة
  Future<void> updatePumpStatus(String systemId, bool isPumpOn, String mode) async {
    try {
      await connectionManager.executeWithRetry(
        () => _firestore.collection('pump_status').doc(systemId).set({
          'isPumpOn': isPumpOn,
          'currentModeId': mode == 'manual' ? 1 : 2,
          'lastUpdated': FieldValue.serverTimestamp(),
        }, SetOptions(merge: true)),
      );
    } catch (e) {
      debugPrint('Error updating pump status: $e');
      rethrow;
    }
  }

  // التحكم اليدوي بالمضخة
  Future<String> manualPumpControl(String activationCode, bool isPumpOn) async {
    try {
      // تحديث حالة المضخة في Realtime Database
      final ref = _realtimeDb.ref('pump_control/$activationCode');
      await connectionManager.executeWithRetry(
        () => ref.set({
          'isPumpOn': isPumpOn,
          'mode': 'manual',
          'timestamp': ServerValue.timestamp,
        }),
      );

      return isPumpOn ? 'تم تشغيل المضخة' : 'تم إيقاف المضخة';
    } catch (e) {
      debugPrint('Error in manual pump control: $e');
      throw Exception('فشل في التحكم بالمضخة');
    }
  }

  // جلب الإعدادات الذكية
  Future<SmartIrrigationSettings> getSmartSettings(String activationCode) async {
    try {
      final doc = await connectionManager.executeWithRetry(
        () => _firestore.collection('smart_settings').doc(activationCode).get(),
      );

      if (!doc.exists) {
        // إرجاع إعدادات افتراضية
        return SmartIrrigationSettings(
          minSoilMoisture: 60.0,
          maxTemperature: 35.0,
          minWaterLevel: 20.0,
          ignoreRain: false,
          optimalHumidity: 60.0,
          updatedAt: DateTime.now(),
        );
      }

      return SmartIrrigationSettings.fromMap(doc.data()!);
    } catch (e) {
      debugPrint('Error getting smart settings: $e');
      return SmartIrrigationSettings(
        minSoilMoisture: 60.0,
        maxTemperature: 35.0,
        minWaterLevel: 20.0,
        ignoreRain: false,
        optimalHumidity: 60.0,
        updatedAt: DateTime.now(),
      );
    }
  }

  // تحديث الإعدادات الذكية
  Future<void> updateSmartSettings(String activationCode, SmartIrrigationSettings settings) async {
    try {
      await connectionManager.executeWithRetry(
        () => _firestore.collection('smart_settings').doc(activationCode).set(
          settings.toMap(),
          SetOptions(merge: true),
        ),
      );
    } catch (e) {
      debugPrint('Error updating smart settings: $e');
      rethrow;
    }
  }

  // التحكم الذكي بالمضخة
  Future<String> smartPumpControl(String activationCode, SmartIrrigationSettings settings) async {
    try {
      // جلب بيانات الحساسات الحالية
      final sensorData = await getSensorData(activationCode);

      final soilMoisture = _toDouble(sensorData['soil_moisture']?['value']);
      final waterLevel = _toDouble(sensorData['water_level']?['value']);
      final temperature = _toDouble(sensorData['temperature']?['value']);
      final rainfall = _toDouble(sensorData['rainfall']?['value']);

      bool shouldStartPump = false;
      bool shouldStopPump = false;

      // منطق التحكم الذكي
      if (soilMoisture < settings.minSoilMoisture &&
          waterLevel > settings.minWaterLevel &&
          temperature < settings.maxTemperature &&
          (settings.ignoreRain || rainfall < 1.0)) {
        shouldStartPump = true;
      } else if (soilMoisture > (settings.minSoilMoisture + 10) ||
                 waterLevel < settings.minWaterLevel ||
                 temperature > settings.maxTemperature ||
                 (!settings.ignoreRain && rainfall > 5.0)) {
        shouldStopPump = true;
      }

      if (shouldStartPump) {
        await _realtimeDb.ref('pump_control/$activationCode').set({
          'isPumpOn': true,
          'mode': 'smart',
          'timestamp': ServerValue.timestamp,
          'reason': 'Smart irrigation conditions met',
        });
        return 'Smart irrigation started';
      } else if (shouldStopPump) {
        await _realtimeDb.ref('pump_control/$activationCode').set({
          'isPumpOn': false,
          'mode': 'smart',
          'timestamp': ServerValue.timestamp,
          'reason': 'Smart irrigation conditions not met',
        });
        return 'Smart irrigation stopped';
      }

      return 'No action needed';
    } catch (e) {
      debugPrint('Error in smart pump control: $e');
      throw Exception('فشل في التحكم الذكي');
    }
  }
