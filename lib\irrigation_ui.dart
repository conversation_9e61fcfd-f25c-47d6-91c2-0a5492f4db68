import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:samaiot_irrigation/IrrigationRecordsScreen.dart';
import 'package:samaiot_irrigation/plant_settings_screen.dart';
import 'irrigation_service.dart';

class MainIrrigationScreen extends StatefulWidget {
  const MainIrrigationScreen({Key? key}) : super(key: key);

  @override
  State<MainIrrigationScreen> createState() => _MainIrrigationScreenState();
}

class _MainIrrigationScreenState extends State<MainIrrigationScreen> {
  final TextEditingController _codeController = TextEditingController();
  late MainIrrigationLogic _logic;
    late BuildContext _scaffoldContext; // أضف هذا السطر لتعريف المتغير


  @override
  void initState() {
    super.initState();
    _logic = MainIrrigationLogic();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    try {
      await _logic.init();
    } catch (e) {
      if (mounted) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar( // استخدام context مباشرة
              SnackBar(content: Text('خطأ في التهيئة: ${e.toString()}')),
            );
          }
        });
      }
    }
  }

  @override
  void dispose() {
    _codeController.dispose();
    _logic.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (builderContext) {
        _scaffoldContext = builderContext;
        return ChangeNotifierProvider.value(
          value: _logic,
          child: Scaffold(
            appBar: AppBar(
              actions: const [ConnectionStatusIndicator()],
              flexibleSpace: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'نظام الري الذكي',
                      style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ),
            ),
            body: Consumer<MainIrrigationLogic>(
              builder: (context, logic, child) {
                if (logic.isLoading && logic.systems.isEmpty) {
                  return const Center(child: CircularProgressIndicator());
                }
                return _buildBody(logic, context);
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildBody(MainIrrigationLogic logic, BuildContext context) {
    if (logic.systems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('لا توجد أنظمة متصلة', style: TextStyle(fontSize: 18, color: Colors.grey)),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              icon: const Icon(Icons.add_link, color: Colors.white),
              label: const Text('اتصال بنظام جديد', style: TextStyle(color: Colors.white)),
              onPressed: () => _showConnectionDialog(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        if (!logic.connectionManager.isConnected) _buildConnectionWarning(),
        Expanded(
          child: RefreshIndicator(
            onRefresh: () async {
              try {
                // تحديث البيانات الحساسات عند السحب لتحديث
                await logic.fetchSensorData();
                if (logic.currentMode == 'smart') {
                  await logic.checkSmartConditions();
                }
              } catch (e) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ في تحديث البيانات: ${e.toString()}')),
                    );
                  }
                });
              }
            },
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildActionButtons(context),
                  const SizedBox(height: 20),
                  const ControlCard(),
                  const SizedBox(height: 20),
                  const SensorCard(),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildConnectionWarning() {
    return Container(
      padding: const EdgeInsets.all(8),
      color: Colors.orange[100],
      child: const Row(
        children: [
          Icon(Icons.warning, color: Colors.orange),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              'الجهاز غير متصل بالخادم - يتم استخدام البيانات المحلية',
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Consumer<MainIrrigationLogic>(
      builder: (context, logic, child) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            _buildActionButton(
              icon: Icons.settings,
              label: 'إدارة الأنظمة',
              color: Colors.blue,
              onPressed: () => _showSystemsManagementDialog(logic, context),
            ),
            _buildActionButton(
              icon: Icons.history,
              label: 'سجلات الري',
              color: Colors.blue,
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => IrrigationRecordsScreen(
                    records: logic.irrigationRecords,
                  ),
                ),
              ),
            ),
            _buildActionButton(
              icon: Icons.eco,
              label: 'إعدادات الري الذكي',
              color: Colors.teal,
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PlantSettingScreen(),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onPressed,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(
          icon: Icon(icon, size: 30),
          color: color,
          onPressed: onPressed,
        ),
        Text(label, style: TextStyle(color: color)),
      ],
    );
  }
void _showSystemsManagementDialog(MainIrrigationLogic logic, BuildContext context) {
  showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('إدارة أنظمة الري', style: TextStyle(fontWeight: FontWeight.bold)),
      content: SizedBox(
        width: double.maxFinite,
        child: ListView.builder(
          shrinkWrap: true,
          itemCount: logic.systems.length,
          itemBuilder: (context, index) {
            final system = logic.systems[index];
            return ListTile(
              leading: Icon(
                Icons.settings,
                color: system.id == logic.selectedSystem ? Colors.green : Colors.grey,
              ),
              title: Text(system.name),
              subtitle: Text(system.serial),
              trailing: IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () async {
                  final confirmDelete = await _showConfirmationDialog(context);
                  if (confirmDelete == true) {
                    final result = await logic.disconnectSystem(
                      activationCode: system.activationCode,
                    );
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text(result['message'])),
                      );
                    }
                  }
                },
              ),
              onTap: () async {
                // Store the context before any async operations
                final dialogContext = context;
                
                // Show loading indicator
                showDialog(
                  context: dialogContext,
                  barrierDismissible: false,
                  builder: (context) => const Center(child: CircularProgressIndicator()),
                );
                
                try {
                  // Select system and update data
                  await logic.selectSystem(system.id);
                  
                  // Close both dialogs
                  if (mounted) {
                    Navigator.of(dialogContext, rootNavigator: true).pop(); // Close loading
                    Navigator.of(dialogContext, rootNavigator: true).pop(); // Close management dialog
                  }
                } catch (e) {
                  if (mounted) {
                    Navigator.of(dialogContext, rootNavigator: true).pop(); // Close loading
                    ScaffoldMessenger.of(dialogContext).showSnackBar(
                      SnackBar(content: Text('خطأ في تحميل بيانات النظام: ${e.toString()}')),
                    );
                  }
                }
              },
            );
          },
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إغلاق', style: TextStyle(color: Colors.grey)),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.pop(context);
            _showConnectionDialog(context);
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          ),
          child: const Text('إضافة نظام', style: TextStyle(color: Colors.white)),
        ),
      ],
    ),
  );
}
  Future<bool?> _showConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا النظام؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false), // إلغاء
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true), // موافق
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }

  void _showConnectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اتصال بنظام جديد'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _codeController,
              decoration: const InputDecoration(
                labelText: 'كود التفعيل',
                hintText: 'أدخل كود التفعيل المكون من 8 أرقام',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_codeController.text.isEmpty) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('الرجاء إدخال كود التفعيل')),
                );
                return;
              }

              // استدعاء connectSystem وتحقق من النتيجة
              final result = await _logic.connectSystem(
                activationCode: _codeController.text,
              );

              if (mounted) {
                Navigator.pop(context); // أغلق مربع الحوار
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(result['message'])),
                );

                // إذا كان هناك خطأ، يمكنك إضافة منطق إضافي هنا إذا لزم الأمر.
                if (result['status'] == 'error') {
                  // قد ترغب في عدم القيام بأي شيء إضافي، أو يمكنك إظهار مزيد من التفاصيل للمستخدم.
                }
              }
            },
            child: const Text('اتصال'),
          ),
        ],
      ),
    );
  }
}

class ConnectionStatusIndicator extends StatelessWidget {
  const ConnectionStatusIndicator({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Provider.of<MainIrrigationLogic>(context, listen: true);
    return Padding(
      padding: const EdgeInsets.only(right: 16),
      child: Icon(
        logic.connectionManager.isConnected ? Icons.wifi : Icons.wifi_off,
        color: logic.connectionManager.isConnected ? Colors.green : Colors.red,
      ),
    );
  }
}

class ControlCard extends StatelessWidget {
  const ControlCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Provider.of<MainIrrigationLogic>(context, listen: true);

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildModeIndicator(logic),
            const SizedBox(height: 12),
            _buildModeSwitcher(logic, context),
            const SizedBox(height: 16),
            _buildModeContent(logic, context),
          ],
        ),
      ),
    );
  }

  Widget _buildModeIndicator(MainIrrigationLogic logic) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (logic.currentMode == 'smart')
          const Icon(Icons.auto_awesome, size: 20, color: Colors.blue),
        if (logic.currentMode == 'manual')
          const Icon(Icons.touch_app, size: 20, color: Colors.green),
        const SizedBox(width: 8),
        Text(
          logic.currentMode == 'smart' ? 'الوضع الذكي' : 'الوضع اليدوي',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: logic.currentMode == 'smart' ? Colors.blue : Colors.green,
          ),
        ),
        if (logic.currentMode == 'smart')
          const Padding(
            padding: EdgeInsets.only(left: 8),
            child: SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(strokeWidth: 2),
            ),
          ),
      ],
    );
  }

  Widget _buildModeSwitcher(MainIrrigationLogic logic, BuildContext context) {
    return ToggleButtons(
      isSelected: [logic.currentMode == 'manual', logic.currentMode == 'smart'],
      onPressed: (index) async {
        final selectedMode = ['manual', 'smart'][index];

        if (selectedMode == 'manual' && logic.currentMode == 'smart') {
          await logic.stopSmartCheck(); // إيقاف الفحص الذكي
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('لقد تم إيقاف الفحص الذكي وعملية الري')),
          );
        }

        if (selectedMode == 'smart') {
          final confirmed = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: const Text('تأكيد'),
              content: const Text('هل أنت متأكد من رغبتك في تشغيل الري الذكي؟'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false), // إلغاء
                  child: const Text('إلغاء'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true), // موافق
                  child: const Text('موافق'),
                ),
              ],
            ),
          );

          if (confirmed != true) return; // إذا تم إلغاء، لا تتقدم
        }

        try {
          await logic.changeMode(selectedMode);
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في تغيير الوضع: ${e.toString()}')),
          );
        }
      },
      borderRadius: BorderRadius.circular(12),
      children: const [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.touch_app),
              SizedBox(height: 4),
              Text('يدوي'),
            ],
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.auto_awesome),
              SizedBox(height: 4),
              Text('ذكي'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModeContent(MainIrrigationLogic logic, BuildContext context) {
    return Column(
      children: [
        if (logic.currentMode == 'smart') _buildSmartModeInfo(),
        const SizedBox(height: 12),
        logic.currentMode == 'manual'
            ? _buildManualControls(logic, context)
            : _buildSmartSettings(logic, context),
      ],
    );
  }

  Widget _buildSmartModeInfo() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.blue.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.info_outline, size: 16, color: Colors.blue),
          SizedBox(width: 4),
          Text('جاري المراقبة التلقائية...', style: TextStyle(color: Colors.blue)),
        ],
      ),
    );
  }

  Widget _buildManualControls(MainIrrigationLogic logic, BuildContext context) {
    return Column(
      children: [
        const Text('التحكم اليدوي', style: TextStyle(fontSize: 16)),
        const SizedBox(height: 8),
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () async {
              try {
                await logic.togglePump();
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('خطأ في التحكم بالمضخة: ${e.toString()}')),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: logic.isPumpOn ? Colors.red : Colors.green,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              logic.isPumpOn ? 'إيقاف المضخة' : 'تشغيل المضخة',
              style: const TextStyle(fontSize: 16, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSmartSettings(MainIrrigationLogic logic, BuildContext context) {
    return Column(
      children: [
        const Text('إعدادات الري الذكي', style: TextStyle(fontSize: 16)),
        const SizedBox(height: 12),
        _buildSmartSlider(
          title: 'رطوبة التربة المطلوبة',
          value: logic.smartSoilMoisture,
          min: 30,
          max: 80,
          unit: '%',
          onChanged: (v) async {
            logic.smartSoilMoisture = v;
            try {
              await logic.updateSmartSettings();
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ في تحديث الإعدادات: ${e.toString()}')),
              );
            }
          },
        ),
        _buildSmartSlider(
          title: 'أقل مستوى للمياه',
          value: logic.smartMinWaterLevel,
          min: 10,
          max: 50,
          unit: '%',
          onChanged: (v) async {
            logic.smartMinWaterLevel = v;
            try {
              await logic.updateSmartSettings();
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ في تحديث الإعدادات: ${e.toString()}')),
              );
            }
          },
        ),
        _buildSmartSlider(
          title: 'أقصى درجة حرارة',
          value: logic.smartMaxTemp,
          min: 20,
          max: 40,
          unit: '°C',
          onChanged: (v) async {
            logic.smartMaxTemp = v;
            try {
              await logic.updateSmartSettings();
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ في تحديث الإعدادات: ${e.toString()}')),
              );
            }
          },
        ),
        SwitchListTile(
          title: const Text('تجاهل المطر'),
          value: logic.smartIgnoreRain,
          onChanged: (v) async {
            logic.smartIgnoreRain = v;
            try {
              await logic.updateSmartSettings();
            } catch (e) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('خطأ في تحديث الإعدادات: ${e.toString()}')),
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildSmartSlider({
    required String title,
    required double value,
    required double min,
    required double max,
    required String unit,
    required ValueChanged<double> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('$title: ${value.toStringAsFixed(1)}$unit'),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: (max - min).toInt(),
            label: '${value.toStringAsFixed(1)}$unit',
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }
}

class SensorCard extends StatelessWidget {
  const SensorCard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final logic = Provider.of<MainIrrigationLogic>(context, listen: true);

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const Text(
              'قراءات الحساسات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 1.2,
              children: [
                ...logic.sensorData.entries.map((entry) => _buildSensorTile(
                      name: entry.value['name_ar'],
                      value: entry.value['value'],
                      unit: entry.value['unit'],
                      icon: entry.value['icon'],
                      color: entry.value['color'],
                    )),
                _buildSensorTile(
                  name: 'حالة المضخة',
                  value: logic.isPumpOn ? 1 : 0,
                  unit: '',
                  icon: Icons.power_settings_new,
                  color: logic.isPumpOn ? Colors.green : Colors.red,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSensorTile({
    required String name,
    required dynamic value,
    required String unit,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 30, color: color),
          const SizedBox(height: 8),
          Text(
            name,
            style: const TextStyle(fontSize: 14),
            textAlign: TextAlign.center,
          ),
          Text(
            _formatValue(value, unit),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _formatValue(dynamic value, String unit) {
    if (value is double) {
      return unit == '°C' ? value.toStringAsFixed(1) : value.toStringAsFixed(0);
    }
    return value == 1 ? 'تعمل' : 'متوقفة';
  }
}