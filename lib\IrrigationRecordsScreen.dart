import 'package:flutter/material.dart';
import 'package:samaiot_irrigation/irrigation_service.dart';

class IrrigationRecordsScreen extends StatelessWidget {
  final List<IrrigationRecord> records;

  const IrrigationRecordsScreen({
    super.key,
    required this.records,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('سجلات الري'),
        centerTitle: true,
        elevation: 0,
      ),
      body: records.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد سجلات متاحة',
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: records.length,
              itemBuilder: (context, index) {
                final record = records[index];
                return Card(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: InkWell(
                    onTap: () {
                      // يمكنك إضافة تفاصيل إضافية عند الضغط على السجل هنا
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                record.mode == 'manual' ? Icons.touch_app : Icons.auto_awesome,
                                color: record.mode == 'manual' ? Colors.blue : Colors.green,
                                size: 32,
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Text(
                                  'وقت البدء: ${record.formattedDate}',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Divider(
                            color: Colors.grey[300],
                            thickness: 1,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'النوع: ${record.formattedMode}',
                            style: const TextStyle(fontSize: 14),
                          ),
                          if (record.duration != null)
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Text(
                                'المدة: ${_formatDuration(record.duration!)}',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                          Text(
                            'كمية المياه: ${record.waterConsumption.toStringAsFixed(2)} لتر',
                            style: const TextStyle(fontSize: 14),
                          ),
                          if (record.initialWaterLevel != null && record.finalWaterLevel != null)
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 4),
                              child: Text(
                                'مستوى المياه: ${record.initialWaterLevel!.toStringAsFixed(1)}% → ${record.finalWaterLevel!.toStringAsFixed(1)}%',
                                style: const TextStyle(fontSize: 14),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}