import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'irrigation_service.dart';

class PlantSettingScreen extends StatefulWidget {
  const PlantSettingScreen({super.key});

  @override
  State<PlantSettingScreen> createState() => _PlantSettingScreenState();
}

class _PlantSettingScreenState extends State<PlantSettingScreen> {
  String? _selectedPlant;
  String? _selectedStage;

  @override
  Widget build(BuildContext context) {
    // تأكد من وجود Provider في شجرة الـ Widgets
    final irrigationLogic = Provider.of<MainIrrigationLogic>(context);
    final plantsData = irrigationLogic.plantsSettings;

    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات النباتات'),
        actions: [
          if (_selectedPlant != null && _selectedStage != null)
            IconButton(
              icon: const Icon(Icons.check),
              onPressed: () async {
                try {
                  await irrigationLogic.applyPlantSettings(_selectedPlant!, _selectedStage!);
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('تم تطبيق إعدادات $_selectedPlant - $_selectedStage')),
                    );
                  }
                } catch (e) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ في تطبيق الإعدادات: ${e.toString()}')),
                    );
                  }
                }
              },
            ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                DropdownButtonFormField<String>(
                  value: _selectedPlant,
                  decoration: InputDecoration(
                    labelText: 'اختر نوع النبات',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    prefixIcon: const Icon(Icons.eco),
                  ),
                  items: plantsData.keys
                      .map((String plant) {
                        return DropdownMenuItem<String>(
                          value: plant,
                          child: Text(plant),
                        );
                      })
                      .toList()
                      .cast<DropdownMenuItem<String>>(), // إضافة cast هنا
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedPlant = newValue;
                      _selectedStage = null; // إعادة تعيين المرحلة عند تغيير النبات
                    });
                  },
                ),
                const SizedBox(height: 16),
                if (_selectedPlant != null)
                  DropdownButtonFormField<String>(
                    value: _selectedStage,
                    decoration: InputDecoration(
                      labelText: 'اختر مرحلة النمو',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.timeline),
                    ),
                    items: plantsData[_selectedPlant]!['stages'].keys
                        .map((String stage) {
                          return DropdownMenuItem<String>(
                            value: stage,
                            child: Text(stage),
                          );
                        })
                        .toList()
                        .cast<DropdownMenuItem<String>>(), // إضافة cast هنا
                    onChanged: (String? newValue) {
                      setState(() {
                        _selectedStage = newValue;
                      });
                    },
                  ),
              ],
            ),
          ),
          Expanded(
            child: _selectedPlant != null && _selectedStage != null
                ? _buildPlantDetails(_selectedPlant!, _selectedStage!, plantsData, irrigationLogic)
                : const Center(
                    child: Text(
                      'اختر نباتاً ومرحلة النمو لعرض إعدادات الري الموصى بها',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  ),
          ),
          if (_selectedPlant != null && _selectedStage != null)
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: ElevatedButton.icon(
                icon: const Icon(Icons.settings),
                label: const Text('تطبيق الإعدادات تلقائياً'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: const Size(double.infinity, 50),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                onPressed: () async {
                  try {
                    await irrigationLogic.applyPlantSettings(_selectedPlant!, _selectedStage!);
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('تم تطبيق إعدادات $_selectedPlant - $_selectedStage')),
                      );
                    }
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('خطأ في تطبيق الإعدادات: ${e.toString()}')),
                      );
                    }
                  }
                },
              ),
            ),
        ],
      ),
    );
  }

  // تابع نتائج النباتات
  Widget _buildPlantDetails(String plantName, String stageName, Map<String, dynamic> plantsData, MainIrrigationLogic irrigationLogic) {
    final plantData = plantsData[plantName]!;
    final stageData = plantData['stages'][stageName]!;
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            elevation: 3,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Row(
                    children: [
                      Icon(plantData['icon'], color: plantData['color'], size: 30),
                      const SizedBox(width: 10),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            plantName,
                            style: TextStyle(
                              fontSize: 22,
                              fontWeight: FontWeight.bold,
                              color: plantData['color'],
                            ),
                          ),
                          Text(
                            'مرحلة $stageName',
                            style: const TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 20),
          _buildSettingCard(
            title: 'إعدادات رطوبة التربة',
            currentValue: irrigationLogic.smartSoilMoisture,
            recommendedRange: '${stageData['soil_moisture']['min']}% - ${stageData['soil_moisture']['max']}%',
            icon: Icons.water_drop,
            color: Colors.blue,
          ),
          const SizedBox(height: 16),
          _buildSettingCard(
            title: 'إعدادات درجة الحرارة',
            currentValue: irrigationLogic.smartMaxTemp,
            recommendedRange: stageData['temperature'].containsKey('day')
                ? 'نهاراً: ${stageData['temperature']['day']['min']}°C - ${stageData['temperature']['day']['max']}°C\nليلاً: ${stageData['temperature']['night']['min']}°C - ${stageData['temperature']['night']['max']}°C'
                : '${stageData['temperature']['min']}°C - ${stageData['temperature']['max']}°C',
            icon: Icons.thermostat,
            color: Colors.orange,
          ),
          const SizedBox(height: 16),
          _buildSettingCard(
            title: 'الرطوبة الجوية',
            currentValue: null,
            recommendedRange: '${stageData['humidity']['min']}% - ${stageData['humidity']['max']}%',
            icon: Icons.cloud,
            color: Colors.lightBlue,
          ),
          const SizedBox(height: 16),
          _buildSettingCard(
            title: 'كمية المياه',
            currentValue: null,
            recommendedRange: stageData['water_amount'],
            icon: Icons.invert_colors,
            color: Colors.indigo,
          ),
          const SizedBox(height: 16),
          Card(
            elevation: 3,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'إرشادات الري',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 10),
                  _buildTipItem('أفضل وقت للري', stageData['best_time']),
                  _buildTipItem('التعامل مع المطر', stageData['rain_action']),
                  _buildTipItem('تحذير مستوى الماء', stageData['water_warning']),
                  if (plantName == 'الموز')
                    _buildTipItem('ملاحظة', 'الري المتكرر ضروري لضمان النمو الأمثل'),
                  if (plantName == 'البصل' && stageName == 'النضج')
                    _buildTipItem('تنبيه', 'إيقاف الري قبل الحصاد ب3 أسابيع لتحسين الجودة'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String recommendedRange,
    required IconData icon,
    required Color color,
    double? currentValue,
  }) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color),
                const SizedBox(width: 10),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              'النطاق الموصى به:',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            Text(
              recommendedRange,
              style: TextStyle(
                fontWeight: FontWeight.bold,
                color: color,
                fontSize: 16,
              ),
            ),
            if (currentValue != null) ...[
              const SizedBox(height: 10),
              Text(
                'القيمة الحالية:',
                style: TextStyle(color: Colors.grey.shade600),
              ),
              Text(
                '${currentValue.toStringAsFixed(1)}${title.contains('الحرارة') ? '°C' : '%'}',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                  fontSize: 16,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildTipItem(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(Icons.info_outline, size: 20, color: Colors.blue.shade700),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  value,
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}