import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class IrrigationSystem {
  final String id;
  final String name;
  final bool isConnected;
  final String serial;
  final String activationCode;

  IrrigationSystem({
    required this.id,
    required this.name,
    required this.isConnected,
    required this.serial,
    required this.activationCode,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'isConnected': isConnected,
      'serial': serial,
      'activationCode': activationCode,
    };
  }

  factory IrrigationSystem.fromMap(Map<String, dynamic> map) {
    return IrrigationSystem(
      id: map['id']?.toString() ?? UniqueKey().toString(),
      name: map['name']?.toString() ?? 'نظام غير معروف',
      isConnected: map['isConnected'] is bool
          ? map['isConnected'] as bool
          : map['isConnected']?.toString().toLowerCase() == 'true',
      serial: map['serial']?.toString() ?? '',
      activationCode: map['activationCode']?.toString() ?? '',
    );
  }
}

class IrrigationRecord {
  final String systemName;
  final DateTime startTime;
  final DateTime? endTime;
  final String mode;
  final double waterConsumption;
  final double? initialWaterLevel;
  final double? finalWaterLevel;

  IrrigationRecord({
    required this.systemName,
    required this.startTime,
    this.endTime,
    required this.mode,
    required this.waterConsumption,
    this.initialWaterLevel,
    this.finalWaterLevel,
  });

  String get formattedDate {
    return '${startTime.day}/${startTime.month}/${startTime.year} ${startTime.hour}:${startTime.minute.toString().padLeft(2, '0')}';
  }

  String get formattedMode {
    return mode == 'manual' ? 'يدوي' : 'ذكي';
  }

  Duration? get duration {
    return endTime?.difference(startTime);
  }
}

double _toDouble(dynamic value, {double defaultValue = 0.0}) {
  if (value == null) return defaultValue;
  if (value is double) return value;
  if (value is int) return value.toDouble();
  throw Exception('Expected double or int but got ${value.runtimeType}');
}

class SmartIrrigationSettings {
  final double minSoilMoisture;
  final double maxTemperature;
  final double minWaterLevel;
  final bool ignoreRain;
  final double optimalHumidity;
  final DateTime updatedAt;

  SmartIrrigationSettings({
    required this.minSoilMoisture,
    required this.maxTemperature,
    required this.minWaterLevel,
    required this.ignoreRain,
    required this.optimalHumidity,
    required this.updatedAt,
  });

  factory SmartIrrigationSettings.fromMap(Map<String, dynamic> map) {
    return SmartIrrigationSettings(
      minSoilMoisture: _toDouble(map['min_soil_moisture'], defaultValue: 60.0),
      maxTemperature: _toDouble(map['max_temperature'], defaultValue: 35.0),
      minWaterLevel: _toDouble(map['min_water_level'], defaultValue: 20.0),
      ignoreRain: map['ignore_rain'] ?? false,
      optimalHumidity: _toDouble(map['optimal_humidity']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }
}



class ConnectionManager {
  final SupabaseClient _supabase;
  bool _isConnected = true;
  final StreamController<bool> _connectionController = StreamController<bool>.broadcast();
  
  ConnectionManager(this._supabase) {
    _initConnectionMonitoring();
  }
  
  void _initConnectionMonitoring() {
    Connectivity().onConnectivityChanged.listen((result) async {
      final newStatus = result != ConnectivityResult.none;
      if (newStatus != _isConnected) {
        _isConnected = newStatus;
        _connectionController.add(_isConnected);
        
        if (_isConnected) {
          try {
            await _supabase.from('irrigation_systems').select().limit(1).timeout(const Duration(seconds: 5));
          } catch (e) {
            _isConnected = false;
            _connectionController.add(false);
          }
        }
      }
    });
  }
  
  Stream<bool> get connectionStream => _connectionController.stream;
  
  Future<T> executeWithRetry<T>(Future<T> Function() action, 
      {int maxRetries = 3, Duration? retryDelay}) async {
    int attempts = 0;
    while (attempts < maxRetries) {
      try {
        final result = await action().timeout(const Duration(seconds: 15));
        _isConnected = true;
        return result;
      } catch (e) {
        attempts++;
        _isConnected = false;
        _connectionController.add(false);
        if (attempts >= maxRetries) rethrow;
        await Future.delayed(retryDelay ?? Duration(seconds: 2 * attempts));
      }
    }
    throw Exception('فشل العملية بعد $maxRetries محاولات');
  }
  
  bool get isConnected => _isConnected;
  
  void dispose() {
    _connectionController.close();
  }
}

class MainIrrigationLogic extends ChangeNotifier {
  bool isPumpOn = false;
  String currentMode = 'manual';
  List<IrrigationSystem> systems = [];
  String? selectedSystem;
  List<IrrigationRecord> irrigationRecords = [];
  DateTime? pumpStartTime;
  Map<String, dynamic> _sensorData = {};
  bool isLoading = false;
  SharedPreferences? _prefs;
  final SupabaseClient _supabase = Supabase.instance.client;
  final ConnectionManager connectionManager;

  double smartSoilMoisture = 60.0;
  double smartMinWaterLevel = 20.0;
  double smartMaxTemp = 35.0;
  bool smartIgnoreRain = false;
  Timer? _dataUpdateTimer;
  Timer? _connectionCheckTimer;

  Map<String, dynamic> _sensorCache = {};
  DateTime? _lastSensorUpdate;

  // إعدادات الهيستريزس والتأخير الزمني
  final double _hysteresisMoisture = 5.0; // منطقة ميتة لرطوبة التربة
  final Duration _minPumpDuration = const Duration(minutes: 3); // الحد الأدنى لزمن التشغيل

  MainIrrigationLogic() : connectionManager = ConnectionManager(Supabase.instance.client) {
    init();
  }
  // Method عام لاستدعاء _checkSmartConditions
  Future<void> checkSmartConditions() async {
    await _checkSmartConditions();
  }
  final int _updateIntervalSeconds = 10;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    await _loadConnectedSystems();
    await validateSelectedSystem();
    
    connectionManager.connectionStream.listen((connected) {
      if (connected) {
        _fetchInitialData();
      }
      notifyListeners();
    });
    
    await _fetchInitialData();
    _startDataUpdates();
    _startConnectionChecks();
  }
  

Future<void> stopSmartCheck() async {
  // تأكد من أن العملية قائمة قبل إيقافها
  if (currentMode == 'smart') {
    // أوقف المؤقت الخاص بتحديث البيانات
    _dataUpdateTimer?.cancel();
    _dataUpdateTimer = null;

    // توقف عن مراقبة ظروف الري الذكي
    // يمكنك أيضًا إضافة أي منطق آخر هنا إذا كان لديك حالة أخرى متعلقة بالتحكم
    // مثل إيقاف المضخة إذا كانت تعمل، بناءً على حالتك الخاصة

    // تعيين الوضع الحالي إلى يدوي وإيقاف المضخة
    isPumpOn = false;
    currentMode = 'manual';
    
    // تحديث قاعدة البيانات أو الحالة اللازمة هنا
    final system = systems.firstWhere((s) => s.id == selectedSystem);
    await connectionManager.executeWithRetry(
      () => _supabase.from('pump_status').upsert({
        'system_id': system.id,
        'is_pump_on': isPumpOn,
        'current_mode_id': 1, // وضع يدوي
      }, onConflict: 'system_id'),
    );

    // إبلاغ المستمعين أن الحالة قد تغيرت
    notifyListeners();

    // عرض رسالة تأكيد (يمكنك إضافة هذا في واجهة المستخدم مباشرة أيضًا)
    debugPrint('تم إيقاف تشغيل الفحص الذكي.');
  }
}

final Map<String, Map<String, dynamic>> _plantsSettings = {
  'الطماطم': {
    'icon': Icons.grass,
    'color': Colors.red,
    'stages': {
      'الإنبات': {
        'soil_moisture': {'min': 70, 'max': 85},
        'temperature': {'min': 20, 'max': 25},
        'humidity': {'min': 60, 'max': 70},
        'rain_action': 'إيقاف الري عند المطر',
        'water_amount': '5-7 مم/يوم',
        'best_time': 'الصباح (6-8 ص)',
        'water_warning': '<30% سعة الخزان',
      },
      'النمو الخضري': {
        'soil_moisture': {'min': 60, 'max': 70},
        'temperature': {'day': {'min': 25, 'max': 28}, 'night': {'min': 18, 'max': 20}},
        'humidity': {'min': 50, 'max': 60},
        'rain_action': 'إيقاف الري عند المطر',
        'water_amount': '80-90 مم/أسبوع',
        'best_time': 'الصباح',
        'water_warning': '<25%',
      },
      'التزهير': {
        'soil_moisture': {'min': 50, 'max': 60},
        'temperature': {'min': 22, 'max': 26},
        'humidity': {'min': 40, 'max': 50},
        'rain_action': 'إيقاف الري عند المطر',
        'water_amount': '50-60 مم/أسبوع',
        'best_time': 'مساءً (بعد 6م)',
        'water_warning': '<20%',
      },
      'الإثمار': {
        'soil_moisture': {'min': 65, 'max': 75},
        'temperature': {'min': 24, 'max': 28},
        'humidity': {'min': 45, 'max': 55},
        'rain_action': 'إيقاف الري عند المطر',
        'water_amount': '70-80 مم/أسبوع',
        'best_time': 'مساءً',
        'water_warning': '<15%',
      },
    },
  },
  'الذرة': {
    'icon': Icons.agriculture,
    'color': Colors.yellow.shade700,
    'stages': {
      'الإنبات': {
        'soil_moisture': {'min': 60, 'max': 70},
        'temperature': {'min': 15, 'max': 25},
        'humidity': {'min': 50, 'max': 60},
        'rain_action': 'إيقاف إذا >5 مم',
        'water_amount': '15-20 مم/أسبوع',
        'best_time': 'صباحاً',
        'water_warning': '<30%',
      },
      'النمو الخضري': {
        'soil_moisture': {'min': 70, 'max': 80},
        'temperature': {'min': 20, 'max': 25},
        'humidity': {'min': 55, 'max': 65},
        'rain_action': 'إيقاف إذا >10 مم',
        'water_amount': '30-40 مم/أسبوع',
        'best_time': 'صباحاً',
        'water_warning': '<25%',
      },
      'التلقيح': {
        'soil_moisture': {'min': 75, 'max': 85},
        'temperature': {'min': 22, 'max': 28},
        'humidity': {'min': 60, 'max': 70},
        'rain_action': 'إيقاف إذا >8 مم',
        'water_amount': '50-60 مم/أسبوع',
        'best_time': 'صباحاً مبكر',
        'water_warning': '<20%',
      },
      'النضج': {
        'soil_moisture': {'min': 50, 'max': 60},
        'temperature': {'min': 20, 'max': 27},
        'humidity': {'min': 45, 'max': 55},
        'rain_action': 'إيقاف الري عند المطر',
        'water_amount': '25-30 مم/أسبوع',
        'best_time': 'مساءً',
        'water_warning': '<15%',
      },
    },
  },
  'الموز': {
    'icon': Icons.park,
    'color': Colors.green,
    'stages': {
      'التأسيس': {
        'soil_moisture': {'min': 80, 'max': 90},
        'temperature': {'min': 25, 'max': 30},
        'humidity': {'min': 70, 'max': 80},
        'rain_action': 'لا توقف إلا إذا >15 مم',
        'water_amount': '20-30 مم/يوم',
        'best_time': '3-4 مرات يومياً',
        'water_warning': '<40%',
      },
      'النمو النشط': {
        'soil_moisture': {'min': 75, 'max': 85},
        'temperature': {'min': 26, 'max': 32},
        'humidity': {'min': 65, 'max': 75},
        'rain_action': 'لا توقف إلا إذا >20 مم',
        'water_amount': '35-45 مم/يوم',
        'best_time': 'كل 6 ساعات',
        'water_warning': '<30%',
      },
      'الإثمار': {
        'soil_moisture': {'min': 70, 'max': 80},
        'temperature': {'min': 24, 'max': 30},
        'humidity': {'min': 60, 'max': 70},
        'rain_action': 'لا توقف إلا إذا >10 مم',
        'water_amount': '25-35 مم/يوم',
        'best_time': 'صباحاً ومساءً',
        'water_warning': '<25%',
      },
    },
  },
  'البصل': {
    'icon': Icons.grass,
    'color': Colors.purple,
    'stages': {
      'الإنبات': {
        'soil_moisture': {'min': 70, 'max': 80},
        'temperature': {'min': 10, 'max': 25},
        'humidity': {'min': 60, 'max': 70},
        'rain_action': 'إيقاف إذا >3 مم',
        'water_amount': '3-4 مرات/أسبوع',
        'best_time': 'صباحاً',
        'water_warning': '<30%',
      },
      'النمو الخضري': {
        'soil_moisture': {'min': 60, 'max': 70},
        'temperature': {'min': 15, 'max': 25},
        'humidity': {'min': 50, 'max': 60},
        'rain_action': 'إيقاف إذا >5 مم',
        'water_amount': '25-30 مم/أسبوع',
        'best_time': 'صباحاً',
        'water_warning': '<25%',
      },
      'النضج': {
        'soil_moisture': {'min': 40, 'max': 50},
        'temperature': {'min': 20, 'max': 28},
        'humidity': {'min': 40, 'max': 50},
        'rain_action': 'إيقاف الري عند المطر',
        'water_amount': 'إيقاف الري قبل الحصاد ب3 أسابيع',
        'best_time': '-',
        'water_warning': '<20%',
      },
    },
  },
  'القات': {
    'icon': Icons.forest,
    'color': Colors.brown,
    'stages': {
      'التأسيس': {
        'soil_moisture': {'min': 50, 'max': 60},
        'temperature': {'min': 20, 'max': 30},
        'humidity': {'min': 50, 'max': 60},
        'rain_action': 'إيقاف إذا >8 مم',
        'water_amount': '20-25 مم/أسبوع',
        'best_time': 'صباحاً',
        'water_warning': '<30%',
      },
      'النمو النشط': {
        'soil_moisture': {'min': 55, 'max': 65},
        'temperature': {'min': 22, 'max': 32},
        'humidity': {'min': 55, 'max': 65},
        'rain_action': 'إيقاف إذا >10 مم',
        'water_amount': '35-45 مم/أسبوع',
        'best_time': 'صباحاً',
        'water_warning': '<25%',
      },
      'القطاف': {
        'soil_moisture': {'min': 45, 'max': 55},
        'temperature': {'min': 20, 'max': 28},
        'humidity': {'min': 45, 'max': 55},
        'rain_action': 'إيقاف الري عند المطر',
        'water_amount': '15-20 مم/أسبوع',
        'best_time': 'مساءً',
        'water_warning': '<20%',
      },
    },
  },
};

Map<String, Map<String, dynamic>> get plantsSettings => _plantsSettings;

Future<void> applyPlantSettings(String plant, String stage) async {
  if (!_plantsSettings.containsKey(plant)) return;
  final plantData = _plantsSettings[plant]!;
  if (!plantData['stages'].containsKey(stage)) return;
  final settings = plantData['stages'][stage]!;

  if (settings.containsKey('soil_moisture') &&
      settings['soil_moisture'].containsKey('min') &&
      settings['soil_moisture'].containsKey('max')) {
    smartSoilMoisture = (settings['soil_moisture']['min'] + settings['soil_moisture']['max']) / 2;
  }

  if (settings.containsKey('temperature')) {
    if (settings['temperature'].containsKey('day')) {
      smartMaxTemp = settings['temperature']['day']['max'].toDouble();
    } else {
      smartMaxTemp = settings['temperature']['max'].toDouble();
    }
  }

  await updateSmartSettings();
}
  void _startDataUpdates() {
    _dataUpdateTimer?.cancel();
    _dataUpdateTimer = Timer.periodic(
      Duration(seconds: _updateIntervalSeconds), 
      (timer) async {
        if (selectedSystem != null && connectionManager.isConnected) {
          try {
            await fetchSensorData();
            if (currentMode == 'smart') {
              await _checkSmartConditions();
            }
          } catch (e) {
            debugPrint('Error in periodic update: $e');
          }
        }
      }
    );
  }

  void _startConnectionChecks() {
    _connectionCheckTimer?.cancel();
    _connectionCheckTimer = Timer.periodic(
      const Duration(seconds: 30),
      (timer) async {
        if (!connectionManager.isConnected) {
          try {
            await _supabase.from('irrigation_systems').select().limit(1).timeout(const Duration(seconds: 5));
            connectionManager._isConnected = true;
            connectionManager._connectionController.add(true);
            notifyListeners();
          } catch (e) {
            debugPrint('Connection check failed: $e');
          }
        }
      }
    );
  }

  Future<void> _checkSmartConditions() async {
    if (!connectionManager.isConnected) return;
    
    try {
      final system = systems.firstWhere((s) => s.id == selectedSystem);
      final result = await connectionManager.executeWithRetry(
        () => _supabase.rpc('smart_pump_control', params: {
          'p_activation_code': system.activationCode,
        }),
      );
      
      debugPrint('''
      Smart control result: $result
      Current sensor values:
        Soil Moisture: ${_sensorData['soil_moisture']?['value']}%
        Water Level: ${_sensorData['water_level']?['value']}%
        Temperature: ${_sensorData['temperature']?['value']}°C
        Rainfall: ${_sensorData['rainfall']?['value']}mm
        Humidity: ${_sensorData['humidity']?['value']}%
      ''');
      
      if (result.contains('started') && !isPumpOn) {
        isPumpOn = true;
        pumpStartTime = DateTime.now();
        recordIrrigation('smart');
        notifyListeners();
      } else if (result.contains('stopped') && isPumpOn) {
        final pumpDuration = DateTime.now().difference(pumpStartTime!);
        if (pumpDuration >= _minPumpDuration) {
          isPumpOn = false;
          _updateIrrigationRecord();
          notifyListeners();
        } else {
          debugPrint('Pump duration too short ($pumpDuration), not stopping yet');
        }
      }
    } catch (e) {
      debugPrint('Error in smart control check: $e');
    }
  }

  void _updateIrrigationRecord() {
    if (pumpStartTime != null) {
      final duration = DateTime.now().difference(pumpStartTime!);
      final lastRecord = irrigationRecords.lastWhere(
        (r) => r.endTime == null,
        orElse: () => irrigationRecords.last,
      );
      
      final currentWaterLevel = _sensorData['water_level']?['value'];
      final waterUsed = (lastRecord.initialWaterLevel ?? 0) - (currentWaterLevel ?? 0);
      
      irrigationRecords[irrigationRecords.indexOf(lastRecord)] = IrrigationRecord(
        systemName: lastRecord.systemName,
        startTime: lastRecord.startTime,
        endTime: DateTime.now(),
        mode: lastRecord.mode,
        waterConsumption: waterUsed > 0 ? waterUsed : 0,
        initialWaterLevel: lastRecord.initialWaterLevel,
        finalWaterLevel: currentWaterLevel,
      );
    }
  }

  Map<String, dynamic> get sensorData {
    return _sensorCache.isNotEmpty ? _sensorCache : _getDefaultSensorValues();
  }

  bool? get isConnected => null;

  Map<String, dynamic> _getDefaultSensorValues() {
    return {
      'soil_moisture': {'value': 0, 'unit': '%', 'name_ar': 'رطوبة التربة', 'icon': Icons.grass, 'color': Colors.grey},
      'water_level': {'value': 0, 'unit': '%', 'name_ar': 'مستوى المياه', 'icon': Icons.water_drop, 'color': Colors.grey},
      'temperature': {'value': 0, 'unit': '°C', 'name_ar': 'درجة الحرارة', 'icon': Icons.thermostat, 'color': Colors.grey},
      'rainfall': {'value': 0, 'unit': 'mm', 'name_ar': 'هطول الأمطار', 'icon': Icons.umbrella, 'color': Colors.grey},
      'humidity': {'value': 0, 'unit': '%', 'name_ar': 'الرطوبة الجوية', 'icon': Icons.cloud, 'color': Colors.grey},
    };
  }

  Future<void> validateSelectedSystem() async {
    if (selectedSystem == null && systems.isNotEmpty) {
      selectedSystem = systems.first.id;
      await _saveConnectedSystems();
    } else if (selectedSystem != null && !systems.any((s) => s.id == selectedSystem)) {
      selectedSystem = systems.isNotEmpty ? systems.first.id : null;
      await _saveConnectedSystems();
    }
    notifyListeners();
  }

  Future<void> updateSmartSettings() async {
    if (selectedSystem == null) return;
    
    setState(() => isLoading = true);
    try {
      final system = systems.firstWhere((s) => s.id == selectedSystem);
      
      final response = await connectionManager.executeWithRetry(
        () => _supabase.rpc('update_smart_irrigation_settings', params: {
          'p_activation_code': system.activationCode,
          'p_preferred_moisture_level': smartSoilMoisture.clamp(30, 80),
          'p_optimal_temperature': smartMaxTemp.clamp(20, 40),
          'p_min_water_level': smartMinWaterLevel.clamp(10, 50),
          'p_max_rainfall': smartIgnoreRain ? 0.0 : 1.0,
          'p_optimal_humidity': 60.0,
        }),
      );

      debugPrint('Smart settings updated: $response');
      
      if (currentMode == 'smart') {
        await _checkSmartConditions();
      }
    } catch (e) {
      debugPrint('Error updating smart settings: $e');
      throw Exception('فشل تحديث الإعدادات الذكية: ${e.toString()}');
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> _loadConnectedSystems() async {
    setState(() => isLoading = true);
    final systemsJson = _prefs?.getStringList('connectedSystems') ?? [];
    systems = systemsJson.map((json) {
      try {
        final map = jsonDecode(json) as Map<String, dynamic>;
        return IrrigationSystem.fromMap(map);
      } catch (e) {
        return IrrigationSystem(
          id: UniqueKey().toString(),
          name: 'نظام معطوب',
          isConnected: false,
          serial: 'unknown',
          activationCode: '',
        );
      }
    }).toList();
    selectedSystem = _prefs?.getString('selectedSystem');
    setState(() => isLoading = false);
  }

  Future<void> _saveConnectedSystems() async {
    final systemsJson = systems.map((sys) => jsonEncode(sys.toMap())).toList();
    await _prefs?.setStringList('connectedSystems', systemsJson);
    if (selectedSystem != null) {
      await _prefs?.setString('selectedSystem', selectedSystem!);
    }
  }

  Future<void> _fetchInitialData() async {
    if (selectedSystem != null) {
      await fetchSensorData();
      await fetchSmartSettings();
      await fetchPumpStatus();
      await fetchIrrigationRecords();
    }
  }
Future<void> fetchSensorData() async {
  if (selectedSystem == null) return;

  setState(() => isLoading = true);
  try {
    if (!connectionManager.isConnected) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final system = systems.firstWhere((s) => s.id == selectedSystem);
    final response = await connectionManager.executeWithRetry(
      () => _supabase.rpc('get_sensor_data_by_activation_code', params: {
        'p_activation_code': system.activationCode,
      }),
    );

    _processSensorResponse(response);
    _lastSensorUpdate = DateTime.now();
    
    // تم إزالة الإشارة إلى _scaffoldContext
    // سيتم التعامل مع عرض الرسائل من خلال الـ BuildContext المقدم من الواجهة
  } on TimeoutException {
    debugPrint('Timeout while fetching sensor data');
    throw Exception('استغرقت العملية وقتاً طويلاً');
  } catch (e) {
    debugPrint('Error fetching sensor data: $e');
    if (_sensorCache.isEmpty) {
      throw Exception('فشل في جلب بيانات الحساسات: ${e.toString()}');
    }
  } finally {
    setState(() => isLoading = false);
  }
}
void _processSensorResponse(dynamic response) {
  final newData = <String, dynamic>{};
  for (final sensor in response) {
    final sensorName = sensor['sensor_name'] as String;
    newData[sensorName] = {
      'value': sensor['sensor_value'] is int ? (sensor['sensor_value'] as int).toDouble() : sensor['sensor_value'],
      'unit': sensor['unit_name'],
      'name_ar': _getSensorArabicName(sensorName),
      'icon': _getSensorIcon(sensorName),
      'color': _getSensorColor(sensorName, sensor['sensor_value'] is int ? (sensor['sensor_value'] as int).toDouble() : sensor['sensor_value']),
    };
  }
  _sensorCache = newData;
  _sensorData = newData;
  notifyListeners();
}
Future<void> fetchSmartSettings() async {
    if (selectedSystem == null) return;
    
    try {
        final system = systems.firstWhere((s) => s.id == selectedSystem);
        final response = await connectionManager.executeWithRetry(
            () => _supabase.rpc('get_current_sensor_settings', params: {
                'p_activation_code': system.activationCode,
            }),
        );

        for (final setting in response) {
            final settingName = setting['setting_name'] as String;
            final value = _toDouble(setting['setting_value']);  // تعديل هنا
            
            if (settingName.contains('Soil Moisture')) {
                smartSoilMoisture = value.clamp(30, 80);
            } else if (settingName.contains('Water Level')) {
                smartMinWaterLevel = value.clamp(10, 50);
            } else if (settingName.contains('Temperature')) {
                smartMaxTemp = value.clamp(20, 40);
            } else if (settingName.contains('Rainfall')) {
                smartIgnoreRain = setting['setting_value'] == 1;
            }
        }
        notifyListeners();
    } catch (e) {
        debugPrint('Error fetching smart settings: $e');
        smartSoilMoisture = 60.0;
        smartMinWaterLevel = 20.0;
        smartMaxTemp = 35.0;
        smartIgnoreRain = false;
    }
}
  Future<void> fetchPumpStatus() async {
    if (selectedSystem == null) return;
    
    try {
      final system = systems.firstWhere((s) => s.id == selectedSystem);
      final response = await connectionManager.executeWithRetry(
        () => _supabase.from('pump_status').select().eq('system_id', system.id).single(),
      );

      isPumpOn = response['is_pump_on'] ?? false;
      currentMode = response['current_mode_id'] == 1 ? 'manual' : 'smart';
      notifyListeners();
    } catch (e) {
      debugPrint('Error fetching pump status: $e');
    }
  }

  Future<void> fetchIrrigationRecords() async {
    if (selectedSystem == null) return;
    
    setState(() => isLoading = true);
    try {
      final system = systems.firstWhere((s) => s.id == selectedSystem);
      final response = await connectionManager.executeWithRetry(
        () => _supabase.rpc('get_irrigation_logs_by_activation_code', params: {
          'p_activation_code': system.activationCode,
        }),
      );

      irrigationRecords = response.map<IrrigationRecord>((log) {
        return IrrigationRecord(
          systemName: log['system_name'] ?? system.name,
          startTime: DateTime.parse(log['start_time']),
          endTime: log['end_time'] != null ? DateTime.parse(log['end_time']) : null,
          mode: log['irrigation_mode'] == 'manual' ? 'manual' : 'smart',
          waterConsumption: log['water_consumption']?.toDouble() ?? 0.0,
          initialWaterLevel: log['initial_water_level']?.toDouble(),
          finalWaterLevel: log['final_water_level']?.toDouble(),
        );
      }).toList();
      notifyListeners();
    } catch (e) {
      debugPrint('Error fetching irrigation records: $e');
    } finally {
      setState(() => isLoading = false);
    }
  }

  String _getSensorArabicName(String sensorName) {
    switch (sensorName) {
      case 'soil_moisture': return 'رطوبة التربة';
      case 'temperature': return 'درجة الحرارة';
      case 'water_level': return 'مستوى المياه';
      case 'rainfall': return 'هطول الأمطار';
      case 'humidity': return 'الرطوبة الجوية';
      default: return sensorName;
    }
  }

  IconData _getSensorIcon(String sensorName) {
    switch (sensorName) {
      case 'soil_moisture': return Icons.grass;
      case 'temperature': return Icons.thermostat;
      case 'water_level': return Icons.water_drop;
      case 'rainfall': return Icons.umbrella;
      case 'humidity': return Icons.cloud;
      default: return Icons.sensors;
    }
  }

  Color _getSensorColor(String sensorName, double value) {
    switch (sensorName) {
      case 'soil_moisture':
        return value < 40 ? Colors.red : 
               value < 60 ? Colors.orange : Colors.green;
      case 'water_level':
        return value < 20 ? Colors.red : 
               value < 50 ? Colors.orange : Colors.blue;
      case 'temperature':
        return value > 35 ? Colors.red : 
               value > 25 ? Colors.orange : Colors.blue;
      case 'humidity':
        return value < 40 ? Colors.blue : 
               value < 70 ? Colors.lightBlue : Colors.blueAccent;
      default: return Colors.blue;
    }
  }

  Future<void> togglePump() async {
    if (selectedSystem == null) return;
    
    setState(() => isLoading = true);
    try {
      final system = systems.firstWhere((s) => s.id == selectedSystem);
      
      if (currentMode == 'manual') {
        final response = await connectionManager.executeWithRetry(
          () => _supabase.rpc('manual_pump_control', params: {
            'p_activation_code': system.activationCode,
            'p_is_pump_on': !isPumpOn,
          }),
        );

        debugPrint('Manual control response: $response');
        
        isPumpOn = !isPumpOn;
        if (isPumpOn) {
          pumpStartTime = DateTime.now();
          recordIrrigation('manual');
        } else if (pumpStartTime != null) {
          _updateIrrigationRecord();
        }
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error toggling pump: $e');
      throw Exception('فشل في التحكم بالمضخة: ${e.toString()}');
    } finally {
      setState(() => isLoading = false);
    }
  }

  Future<void> changeMode(String mode) async {
    if (selectedSystem == null) return;
    
    setState(() => isLoading = true);
    try {
      currentMode = mode;
      if (mode != 'manual') isPumpOn = false;
      
      final system = systems.firstWhere((s) => s.id == selectedSystem);
      await connectionManager.executeWithRetry(
        () => _supabase.from('pump_status').upsert({
          'system_id': system.id,
          'is_pump_on': isPumpOn,
          'current_mode_id': mode == 'manual' ? 1 : 2,
        }, onConflict: 'system_id'),
      );
      
      if (mode == 'smart') {
        await _checkSmartConditions();
      }
      notifyListeners();
    } catch (e) {
      debugPrint('Error changing mode: $e');
      currentMode = currentMode == 'manual' ? 'smart' : 'manual';
      rethrow;
    } finally {
      setState(() => isLoading = false);
    }
  }

  void recordIrrigation(String mode) {
    final currentSystem = systems.firstWhere((s) => s.id == selectedSystem);
    final waterLevel = _sensorData['water_level']?['value'];
    
    final newRecord = IrrigationRecord(
      systemName: currentSystem.name,
      startTime: DateTime.now(),
      mode: mode,
      waterConsumption: 0, // سيتم تحديثه عند إيقاف المضخة
      initialWaterLevel: waterLevel,
    );
    irrigationRecords.add(newRecord);
    notifyListeners();
  }
Future<void> selectSystem(String systemId) async {
  setState(() => isLoading = true);
  try {
    selectedSystem = systemId;
    await _saveConnectedSystems();
    
    // تحديث جميع البيانات فور اختيار النظام الجديد
    await Future.wait([
      fetchSensorData(),
      fetchSmartSettings(),
      fetchPumpStatus(),
      fetchIrrigationRecords(),
    ]);
    
    notifyListeners();
  } catch (e) {
    debugPrint('Error selecting system: $e');
  
  } finally {
    setState(() => isLoading = false);
  }
}

Future<Map<String, dynamic>> connectSystem({
  required String activationCode,
}) async {
  setState(() => isLoading = true);

  try {
    // محاولة للاتصال بالنظام
    final response = await connectionManager.executeWithRetry(
      () => _supabase.rpc('toggle_system_connection', params: {
        'p_activation_code': activationCode,
        'p_connect': true,
      }),
    );

    // استرجاع معلومات النظام بناءً على كود التفعيل
    final systemResponse = await connectionManager.executeWithRetry(
      () => _supabase.from('irrigation_systems').select().eq('activation_code', activationCode).single(),
    );

    // تحقق مما إذا كانت الاستجابة تتضمن النظام
    if (systemResponse == null) {
      return {
        'status': 'error',
        'message': 'فشل الاتصال: النظام غير موجود أو الكود خاطئ.',
      };
    }

    final newSystem = IrrigationSystem(
      id: systemResponse['id'].toString(),
      name: systemResponse['system_name'] ?? 'نظام ${activationCode.substring(0, 4)}',
      isConnected: true,
      serial: systemResponse['esp32_serial'] ?? 'UNKNOWN',
      activationCode: activationCode,
    );

    systems.add(newSystem);
    selectedSystem = newSystem.id;
    await _saveConnectedSystems();
    await _fetchInitialData();

    return {
      'status': 'success',
      'message': 'تم الاتصال بنجاح',
      'system_id': newSystem.id,
    };
  } on PostgrestException catch (e) {
    return {
      'status': 'error',
      'message': 'فشل الاتصال: ${e.message}',
    };
  } catch (e) {
    return {
      'status': 'error',
      'message': 'حدث خطأ غير متوقع',
    };
  } finally {
    setState(() => isLoading = false);
  }
}

  Future<Map<String, dynamic>> disconnectSystem({
    required String activationCode,
  }) async {
    setState(() => isLoading = true);
    
    try {
      final response = await connectionManager.executeWithRetry(
        () => _supabase.rpc('toggle_system_connection', params: {
          'p_activation_code': activationCode,
          'p_connect': false,
        }),
      );
      
      systems.removeWhere((sys) => sys.activationCode == activationCode);
      
      if (selectedSystem != null && 
          systems.any((s) => s.id == selectedSystem) == false) {
        selectedSystem = systems.isNotEmpty ? systems.first.id : null;
      }
      
      await _saveConnectedSystems();
      
      return {
        'status': 'success',
        'message': 'تم قطع الاتصال بنجاح',
        'remaining_connections': systems.length,
      };
    } catch (e) {
      return {
        'status': 'error',
        'message': 'فشل قطع الاتصال: ${e.toString()}',
      };
    } finally {
      setState(() => isLoading = false);
    }
  }

  void setState(VoidCallback fn) {
    fn();
    notifyListeners();
  }

  @override
  void dispose() {
    _dataUpdateTimer?.cancel();
    _connectionCheckTimer?.cancel();
    connectionManager.dispose();
    super.dispose();
  }
}